# 🚀 微信读书MCP服务器 - 快速开始

## 一分钟快速上手

### 第一步：运行一键配置脚本
```bash
python run.py
```

### 第二步：选择"配置Cookie"
在菜单中选择 `1. 🍪 配置Cookie`，按照提示获取并配置你的微信读书Cookie。

### 第三步：测试功能
在菜单中选择 `2. 🧪 运行功能演示`，验证所有功能是否正常工作。

### 第四步：启动MCP服务器
在菜单中选择 `3. 🚀 启动MCP服务器`，服务器将开始运行。

## 🍪 获取Cookie的三种方法

### 方法一：使用Cookie助手（推荐新手）
```bash
python tools/cookie_helper.py
```
交互式工具，会一步步指导你获取Cookie。

### 方法二：使用浏览器书签（最快速）
1. 访问 https://weread.qq.com/ 并登录
2. 复制 `tools/bookmarklet.js` 中的代码
3. 在浏览器地址栏粘贴并回车
4. 弹窗会显示你的Cookie，点击复制即可

### 方法三：手动获取（适合有经验的用户）
1. 登录微信读书网页版
2. 按F12打开开发者工具
3. 切换到Network标签
4. 刷新页面
5. 找到weread.qq.com的请求
6. 复制请求头中的Cookie

## 🔧 在Claude Desktop中配置

1. 找到Claude Desktop的配置文件：
   - Windows: `%APPDATA%\Claude\claude_desktop_config.json`
   - macOS: `~/Library/Application Support/Claude/claude_desktop_config.json`

2. 添加以下配置：
```json
{
  "mcpServers": {
    "wechat-reading": {
      "command": "python",
      "args": ["-m", "wechat_reading_mcp.server"],
      "cwd": "/path/to/wechat-reading-mcp"
    }
  }
}
```

3. 重启Claude Desktop

## 🎯 可用功能

### 📚 获取书籍列表
```
请帮我获取我的在读书籍列表
```

### 📖 查看书籍详情
```
请获取书籍ID为123456的详细信息
```

### 📝 获取读书笔记
```
请获取我最近的10条读书笔记
```

### 🔍 搜索书籍
```
请搜索关于"人工智能"的书籍
```

## ❓ 常见问题

### Q: Cookie获取失败怎么办？
A: 
1. 确保已经登录微信读书网页版
2. 尝试清除浏览器缓存后重新登录
3. 使用无痕模式重新获取
4. 查看详细教程：`docs/获取Cookie指南.md`

### Q: 服务器启动失败？
A:
1. 检查Python版本（需要3.8+）
2. 确保已安装所有依赖：`python install.py`
3. 检查Cookie是否正确配置
4. 查看错误日志：`logs/wechat_reading_mcp.log`

### Q: API请求失败？
A:
1. 检查网络连接
2. Cookie可能已过期，重新获取
3. 微信读书可能更新了API，请报告问题

### Q: 在Claude Desktop中看不到工具？
A:
1. 确保配置文件路径正确
2. 检查JSON格式是否正确
3. 重启Claude Desktop
4. 确保MCP服务器正在运行

## 📞 获取帮助

- 📖 查看完整文档：`README.md`
- 🍪 Cookie获取指南：`docs/获取Cookie指南.md`
- 🐛 遇到问题？请提供详细的错误信息和日志

## 🎉 开始使用

现在你已经准备好使用微信读书MCP服务器了！

运行 `python run.py` 开始你的微信读书AI助手之旅吧！
