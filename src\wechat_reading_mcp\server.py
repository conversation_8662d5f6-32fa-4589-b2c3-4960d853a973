"""
微信读书MCP服务器主模块

实现MCP协议的服务器端，提供微信读书数据访问功能。
"""

import asyncio
import json
import sys
from typing import Any, Dict, List, Optional, Sequence

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    CallToolRequest,
    CallToolResult,
    ListToolsRequest,
    TextContent,
    Tool,
)
from loguru import logger
from dotenv import load_dotenv

from .config import Config
from .wechat_api import WeChatReadingAPI
from .tools import (
    get_book_list_tool,
    get_book_details_tool,
    get_notes_tool,
    search_books_tool,
)


class WeChatReadingMCPServer:
    """微信读书MCP服务器"""
    
    def __init__(self):
        """初始化服务器"""
        # 加载环境变量
        load_dotenv()
        
        # 初始化配置
        self.config = Config()
        
        # 初始化微信读书API客户端
        self.wechat_api = WeChatReadingAPI(self.config)
        
        # 创建MCP服务器实例
        self.server = Server("wechat-reading-mcp")
        
        # 注册工具处理器
        self._register_handlers()
        
        # 配置日志
        self._setup_logging()
    
    def _setup_logging(self):
        """配置日志系统"""
        logger.remove()  # 移除默认处理器
        
        # 添加控制台日志
        logger.add(
            sys.stderr,
            level=self.config.log_level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
                   "<level>{message}</level>"
        )
        
        # 添加文件日志
        if self.config.log_file:
            logger.add(
                self.config.log_file,
                level=self.config.log_level,
                format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
                rotation="10 MB",
                retention="7 days"
            )
    
    def _register_handlers(self):
        """注册MCP处理器"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """列出所有可用的工具"""
            logger.info("收到工具列表请求")
            
            tools = [
                Tool(
                    name="get_book_list",
                    description="获取用户的阅读书籍列表",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "status": {
                                "type": "string",
                                "enum": ["reading", "finished", "wishlist", "all"],
                                "description": "书籍状态：reading(在读)、finished(已读)、wishlist(想读)、all(全部)",
                                "default": "all"
                            },
                            "limit": {
                                "type": "integer",
                                "description": "返回结果数量限制",
                                "default": 20,
                                "minimum": 1,
                                "maximum": 100
                            }
                        }
                    }
                ),
                Tool(
                    name="get_book_details",
                    description="获取指定书籍的详细信息",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "book_id": {
                                "type": "string",
                                "description": "书籍ID"
                            }
                        },
                        "required": ["book_id"]
                    }
                ),
                Tool(
                    name="get_notes",
                    description="获取用户的读书笔记和划线内容",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "book_id": {
                                "type": "string",
                                "description": "书籍ID（可选，不指定则获取所有笔记）"
                            },
                            "limit": {
                                "type": "integer",
                                "description": "返回结果数量限制",
                                "default": 50,
                                "minimum": 1,
                                "maximum": 200
                            }
                        }
                    }
                ),
                Tool(
                    name="search_books",
                    description="搜索书籍",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "搜索关键词（书名、作者等）"
                            },
                            "limit": {
                                "type": "integer",
                                "description": "返回结果数量限制",
                                "default": 20,
                                "minimum": 1,
                                "maximum": 50
                            }
                        },
                        "required": ["query"]
                    }
                )
            ]
            
            logger.info(f"返回 {len(tools)} 个工具")
            return tools
        
        @self.server.call_tool()
        async def handle_call_tool(
            name: str, arguments: Optional[Dict[str, Any]]
        ) -> List[TextContent]:
            """处理工具调用请求"""
            logger.info(f"收到工具调用请求: {name}, 参数: {arguments}")
            
            try:
                if name == "get_book_list":
                    result = await get_book_list_tool(self.wechat_api, arguments or {})
                elif name == "get_book_details":
                    result = await get_book_details_tool(self.wechat_api, arguments or {})
                elif name == "get_notes":
                    result = await get_notes_tool(self.wechat_api, arguments or {})
                elif name == "search_books":
                    result = await search_books_tool(self.wechat_api, arguments or {})
                else:
                    raise ValueError(f"未知的工具: {name}")
                
                logger.info(f"工具 {name} 执行成功")
                return [TextContent(type="text", text=json.dumps(result, ensure_ascii=False, indent=2))]
                
            except Exception as e:
                logger.error(f"工具 {name} 执行失败: {str(e)}")
                error_result = {
                    "error": str(e),
                    "tool": name,
                    "arguments": arguments
                }
                return [TextContent(type="text", text=json.dumps(error_result, ensure_ascii=False, indent=2))]
    
    async def run(self):
        """运行MCP服务器"""
        logger.info("启动微信读书MCP服务器")
        
        # 验证微信读书API连接
        try:
            await self.wechat_api.validate_connection()
            logger.info("微信读书API连接验证成功")
        except Exception as e:
            logger.error(f"微信读书API连接验证失败: {str(e)}")
            logger.warning("服务器将继续运行，但功能可能受限")
        
        # 运行服务器
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="wechat-reading-mcp",
                    server_version="0.1.0",
                    capabilities=self.server.get_capabilities(
                        notification_options=None,
                        experimental_capabilities=None,
                    ),
                ),
            )


async def main():
    """主函数"""
    server = WeChatReadingMCPServer()
    await server.run()


if __name__ == "__main__":
    asyncio.run(main())
